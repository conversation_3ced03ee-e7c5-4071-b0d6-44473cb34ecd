// app/[locale]/admin/customers/page.tsx
// Admin customers list page

import { getTranslations } from 'next-intl/server';
import { CustomersTable } from '@/components/admin/CustomersTable';
import { getAllCustomers } from '@/lib/actions/admin/customer.actions';

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('customerManagement'),
  };
}

export default async function AdminCustomersPage({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const resolvedSearchParams = await searchParams;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const search = typeof resolvedSearchParams.search === 'string' ? resolvedSearchParams.search : undefined;
  const page = typeof resolvedSearchParams.page === 'string' ? parseInt(resolvedSearchParams.page) : 1;

  const customersData = await getAllCustomers({ search, page });

  if ('error' in customersData) {
    return (
      <div className="text-center py-12">
        <p className="text-destructive">{customersData.error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">{t('customerManagement')}</h1>
        <p className="text-muted-foreground mt-2">
          Manage customers and their roles
        </p>
      </div>

      {/* Customers Table */}
      <CustomersTable
        customers={customersData.data}
        total={customersData.total}
        page={customersData.page}
        totalPages={customersData.totalPages}
        locale={locale}
      />
    </div>
  );
}
