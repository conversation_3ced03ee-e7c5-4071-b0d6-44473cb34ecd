// app/[locale]/admin/layout.tsx
// Admin layout with RBAC protection

import { redirect } from 'next/navigation';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';
import { AdminSidebar } from '@/components/admin/AdminSidebar';
import { AdminHeader } from '@/components/admin/AdminHeader';

interface LayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function AdminLayout({ children, params }: LayoutProps) {
  const { locale } = await params;
  const user = await getCurrentUser();

  // Redirect to login if not authenticated
  if (!user) {
    redirect(`/${locale}/login?redirect=/${locale}/admin`);
  }

  // Check admin permission
  const hasAdminAccess = await checkPermission(
    user.uid,
    PermissionAction.ACCESS_ADMIN_DASHBOARD
  );

  if (!hasAdminAccess) {
    // Not authorized, redirect to account
    redirect(`/${locale}/account`);
  }

  return (
    <div className="flex min-h-screen">
      <AdminSidebar locale={locale} />

      <div className="flex-1 flex flex-col">
        <AdminHeader locale={locale} userName={user.fullName} />

        <main className="flex-1 p-8 bg-background">
          {children}
        </main>
      </div>
    </div>
  );
}
