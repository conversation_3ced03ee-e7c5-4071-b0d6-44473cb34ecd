// app/[locale]/admin/warehouse/page.tsx
// Admin warehouse receipts list page

import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { WarehouseReceiptsTable } from '@/components/admin/WarehouseReceiptsTable';
import { getAllWarehouseReceipts } from '@/lib/actions/admin/warehouse.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { PermissionAction, WarehouseReceiptStatus } from '@/app/generated/prisma';

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('warehouseManagement'),
  };
}

export default async function AdminWarehousePage({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const resolvedSearchParams = await searchParams;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const status = typeof resolvedSearchParams.status === 'string' ? resolvedSearchParams.status as WarehouseReceiptStatus : undefined;
  const marketplaceOrderId = typeof resolvedSearchParams.marketplaceOrderId === 'string' ? resolvedSearchParams.marketplaceOrderId : undefined;
  const page = typeof resolvedSearchParams.page === 'string' ? parseInt(resolvedSearchParams.page) : 1;

  const receiptsData = await getAllWarehouseReceipts({ status, marketplaceOrderId, page });

  if ('error' in receiptsData) {
    return (
      <div className="text-center py-12">
        <p className="text-destructive">{receiptsData.error}</p>
      </div>
    );
  }

  // Check if user can create receipts
  const user = await getCurrentUser();
  const canCreate = user ? await checkPermission(user.uid, PermissionAction.WAREHOUSE_RECEIPT_CREATE) : false;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t('warehouseManagement')}</h1>
          <p className="text-muted-foreground mt-2">
            {t('warehouseDescription')}
          </p>
        </div>
        {canCreate && (
          <Link href={`/${locale}/admin/warehouse/new`}>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('createReceipt')}
            </Button>
          </Link>
        )}
      </div>

      {/* Warehouse Receipts Table */}
      <WarehouseReceiptsTable
        receipts={receiptsData.data}
        total={receiptsData.total}
        page={receiptsData.page}
        totalPages={receiptsData.totalPages}
        locale={locale}
      />
    </div>
  );
}

