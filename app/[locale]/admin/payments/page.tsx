// app/[locale]/admin/payments/page.tsx
// Admin payments list page

import { getTranslations } from 'next-intl/server';
import { PaymentsTable } from '@/components/admin/PaymentsTable';
import { getAllPayments } from '@/lib/actions/admin/payment.actions';
import { PaymentStatus } from '@/app/generated/prisma';

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  return {
    title: t('paymentManagement'),
  };
}

export default async function AdminPaymentsPage({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const resolvedSearchParams = await searchParams;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const status = typeof resolvedSearchParams.status === 'string' ? resolvedSearchParams.status as PaymentStatus : undefined;
  const orderId = typeof resolvedSearchParams.orderId === 'string' ? resolvedSearchParams.orderId : undefined;
  const page = typeof resolvedSearchParams.page === 'string' ? parseInt(resolvedSearchParams.page) : 1;

  const paymentsData = await getAllPayments({ status, orderId, page });

  if ('error' in paymentsData) {
    return (
      <div className="text-center py-12">
        <p className="text-destructive">{paymentsData.error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">{t('paymentManagement')}</h1>
        <p className="text-muted-foreground mt-2">
          {t('paymentsDescription')}
        </p>
      </div>

      {/* Payments Table */}
      <PaymentsTable
        payments={paymentsData.data}
        total={paymentsData.total}
        page={paymentsData.page}
        totalPages={paymentsData.totalPages}
        locale={locale}
      />
    </div>
  );
}

