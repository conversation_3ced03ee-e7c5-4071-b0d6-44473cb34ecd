// app/[locale]/page.tsx
// Homepage with featured products and categories

import Link from 'next/link';
import { getTranslations } from 'next-intl/server';
import { ArrowRight, Package, Truck, Shield, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ProductCard } from '@/components/products/ProductCard';
import { getFeaturedProducts, getCategories } from '@/lib/actions/product.actions';
import type { ProductListItem } from '@/lib/types';

type CategoryWithTranslations = {
  id: number;
  parent_id: number | null;
  created: Date;
  updated: Date;
  translations: {
    id: bigint;
    category_id: number;
    language_code: string;
    name: string;
    slug: string;
  }[];
  _count: {
    products: number;
  };
};

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'home' });
  const { SITE_CONFIG, LOCALES } = await import('@/lib/constants');

  const title = t('seoTitle') || 'Chinese Marketplace - Premium Products from Taobao, Pinduoduo & Alibaba';
  const description = t('seoDescription') || 'Discover authentic Chinese products with fast shipping worldwide. Shop electronics, fashion, and more from trusted marketplaces like Taobao, Pinduoduo, and Alibaba.';
  const canonicalUrl = `${SITE_CONFIG.url}/${locale}`;

  return {
    title,
    description,
    keywords: ['Chinese marketplace', 'Taobao', 'Pinduoduo', 'Alibaba', 'e-commerce', 'shopping', 'China products', 'international shipping'],
    alternates: {
      canonical: canonicalUrl,
      languages: LOCALES.reduce((acc, lang) => {
        acc[lang] = `${SITE_CONFIG.url}/${lang}`;
        return acc;
      }, {} as Record<string, string>),
    },
    openGraph: {
      title,
      description,
      type: 'website',
      url: canonicalUrl,
      locale: locale,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    },
  };
}

export default async function HomePage({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'home' });

  // Fetch featured products and categories
  const [featuredProducts, categories] = await Promise.all([
    getFeaturedProducts(locale, 8),
    getCategories(locale),
  ]);

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary/10 via-primary/5 to-background py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center space-y-6">
            <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
              {t('heroTitle')}
            </h1>
            <p className="text-xl text-muted-foreground">
              {t('heroSubtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href={`/${locale}/products`}>
                <Button size="lg" className="w-full sm:w-auto">
                  {t('shopNow')}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="pt-6 text-center space-y-2">
              <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                <Package className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold">{t('feature1Title')}</h3>
              <p className="text-sm text-muted-foreground">
                {t('feature1Description')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6 text-center space-y-2">
              <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                <Truck className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold">{t('feature2Title')}</h3>
              <p className="text-sm text-muted-foreground">
                {t('feature2Description')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6 text-center space-y-2">
              <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                <Shield className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold">{t('feature3Title')}</h3>
              <p className="text-sm text-muted-foreground">
                {t('feature3Description')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6 text-center space-y-2">
              <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                <Globe className="h-6 w-6 text-primary" />
              </div>
              <h3 className="font-semibold">{t('feature4Title')}</h3>
              <p className="text-sm text-muted-foreground">
                {t('feature4Description')}
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Featured Products */}
      {featuredProducts.length > 0 && (
        <section className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-3xl font-bold">{t('featuredProducts')}</h2>
              <p className="text-muted-foreground mt-2">
                {t('featuredProductsDescription')}
              </p>
            </div>
            <Link href={`/${locale}/products`}>
              <Button variant="outline">
                {t('viewAll')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {featuredProducts.map((product) => (
              <ProductCard
                key={product.id}
                product={product}
                locale={locale}
              />
            ))}
          </div>
        </section>
      )}

      {/* Categories */}
      {categories.length > 0 && (
        <section className="container mx-auto px-4">
          <div className="mb-8">
            <h2 className="text-3xl font-bold">{t('shopByCategory')}</h2>
            <p className="text-muted-foreground mt-2">
              {t('shopByCategoryDescription')}
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {categories.slice(0, 10).map((category) => {
              const categoryTranslation = category.translations[0];
              const categoryName = categoryTranslation?.name || 'Category';
              const productCount = category._count?.products || 0;

              return (
                <Link
                  key={category.id}
                  href={`/${locale}/products?category=${category.id}`}
                >
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                    <CardContent className="p-6 text-center space-y-2">
                      <h3 className="font-semibold">{categoryName}</h3>
                      <p className="text-sm text-muted-foreground">
                        {productCount} {t('products')}
                      </p>
                    </CardContent>
                  </Card>
                </Link>
              );
            })}
          </div>
        </section>
      )}
    </div>
  );
}