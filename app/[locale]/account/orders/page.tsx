// app/[locale]/account/orders/page.tsx
// Order history page

import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { Package } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { getUserOrders } from '@/lib/actions/order.actions';
import { formatCurrency, formatDate } from '@/lib/utils';
import { ORDER_STATUS_CONFIG } from '@/lib/constants';

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ page?: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'account' });

  return {
    title: t('orders'),
  };
}

export default async function OrdersPage({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const search = await searchParams;
  const t = await getTranslations({ locale, namespace: 'account' });

  const page = search.page ? parseInt(search.page) : 1;
  const ordersData = await getUserOrders(page);

  if (!ordersData) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">{t('errorLoadingOrders')}</p>
      </div>
    );
  }

  const { orders, pagination } = ordersData;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">{t('orderHistory')}</h2>
        <p className="text-sm text-muted-foreground">
          {t('totalOrders')}: {pagination.total}
        </p>
      </div>

      {orders.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Package className="h-12 w-12 mx-auto mb-4 opacity-50 text-muted-foreground" />
            <p className="text-muted-foreground mb-4">{t('noOrders')}</p>
            <Link href={`/${locale}/products`}>
              <Button>{t('startShopping')}</Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="space-y-4">
            {orders.map((order) => {
              const statusConfig = ORDER_STATUS_CONFIG[order.status as keyof typeof ORDER_STATUS_CONFIG];
              return (
                <Card key={order.id}>
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <p className="font-semibold">Order #{order.id.slice(0, 8)}</p>
                          <Badge variant={statusConfig?.color === 'green' ? 'default' : 'secondary'}>
                            {statusConfig?.label || order.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {t('placed')}: {formatDate(order.created)}
                        </p>
                        <p className="text-sm">
                          {order.order_items.length} {t('items')} • {' '}
                          <span className="font-semibold">
                            {formatCurrency(Number(order.total_amount), order.currency)}
                          </span>
                        </p>
                      </div>
                      <Link href={`/${locale}/account/orders/${order.id}`}>
                        <Button variant="outline">
                          {t('viewDetails')}
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-center gap-2">
              <Link
                href={`/${locale}/account/orders?page=${pagination.page - 1}`}
                className={pagination.page === 1 ? 'pointer-events-none' : ''}
              >
                <Button
                  variant="outline"
                  disabled={pagination.page === 1}
                >
                  {t('previous')}
                </Button>
              </Link>

              <span className="text-sm text-muted-foreground px-4">
                Page {pagination.page} of {pagination.totalPages}
              </span>

              <Link
                href={`/${locale}/account/orders?page=${pagination.page + 1}`}
                className={!pagination.hasMore ? 'pointer-events-none' : ''}
              >
                <Button
                  variant="outline"
                  disabled={!pagination.hasMore}
                >
                  {t('next')}
                </Button>
              </Link>
            </div>
          )}
        </>
      )}
    </div>
  );
}
