-- Performance optimization indexes for large-scale operations
-- These indexes support the optimizations made to product listing, pricing, and order creation

-- Products table indexes for optimized product listing
CREATE INDEX CONCURRENTLY IF NOT EXISTS "products_can_show_created_id_idx" ON "products"("can_show", "created" DESC, "id" DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS "products_can_show_marketplace_idx" ON "products"("can_show", "marketplace");

-- Product translations index for search functionality
CREATE INDEX CONCURRENTLY IF NOT EXISTS "product_translations_language_code_name_idx" ON "product_translations"("language_code", "name");

-- Offers table index for optimized price fetching
CREATE INDEX CONCURRENTLY IF NOT EXISTS "offers_product_id_price_low_idx" ON "offers"("product_id", "price_low" ASC);

-- Pricing rules index for optimized rule fetching
CREATE INDEX CONCURRENTLY IF NOT EXISTS "pricing_rules_is_active_priority_idx" ON "pricing_rules"("is_active", "priority" ASC);
