-- CreateEnum
CREATE TYPE "public"."UserRole" AS ENUM ('USER', 'ADMIN');

-- C<PERSON><PERSON>num
CREATE TYPE "public"."Marketplace" AS ENUM ('taobao', 'pinduoduo', 'alibaba');

-- CreateEnum
CREATE TYPE "public"."ProductImageType" AS ENUM ('preview', 'description', 'video');

-- CreateEnum
CREATE TYPE "public"."OrderStatus" AS ENUM ('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded');

-- CreateEnum
CREATE TYPE "public"."WarehouseReceiptStatus" AS ENUM ('pending', 'matched', 'shipped');

-- CreateEnum
CREATE TYPE "public"."PaymentStatus" AS ENUM ('pending', 'succeeded', 'failed', 'refunded');

-- CreateEnum
CREATE TYPE "public"."UserActivityType" AS ENUM ('view', 'cart_add', 'purchase');

-- CreateEnum
CREATE TYPE "public"."SimilarityType" AS ENUM ('attributes', 'purchased_together', 'marketplace_trend');

-- CreateEnum
CREATE TYPE "public"."PermissionAction" AS ENUM ('ACCESS_ADMIN_DASHBOARD', 'VIEW_DASHBOARD_STATS', 'PRODUCT_CREATE', 'PRODUCT_READ', 'PRODUCT_UPDATE', 'PRODUCT_DELETE', 'PRODUCT_MANAGE_CATEGORIES', 'PRODUCT_MANAGE_VARIANTS', 'PRODUCT_MANAGE_IMAGES', 'ORDER_READ_ALL', 'ORDER_READ_OWN', 'ORDER_UPDATE_STATUS', 'ORDER_CANCEL', 'ORDER_REFUND', 'ORDER_DELETE', 'ORDER_UPDATE_MARKETPLACE_INFO', 'CUSTOMER_READ', 'CUSTOMER_UPDATE', 'CUSTOMER_DELETE', 'CUSTOMER_ASSIGN_ROLE', 'CUSTOMER_REMOVE_ROLE', 'CUSTOMER_VIEW_SENSITIVE_DATA', 'PRICING_RULE_CREATE', 'PRICING_RULE_READ', 'PRICING_RULE_UPDATE', 'PRICING_RULE_DELETE', 'WAREHOUSE_RECEIPT_CREATE', 'WAREHOUSE_RECEIPT_READ', 'WAREHOUSE_RECEIPT_UPDATE', 'WAREHOUSE_RECEIPT_DELETE', 'WAREHOUSE_MATCH_PACKAGE', 'WAREHOUSE_UPDATE_TRACKING', 'WAREHOUSE_VIEW_ALL', 'PAYMENT_CREATE', 'PAYMENT_READ', 'PAYMENT_UPDATE_STATUS', 'PAYMENT_REFUND', 'PAYMENT_VIEW_SENSITIVE_DATA', 'PAYMENT_DELETE', 'ROLE_CREATE', 'ROLE_READ', 'ROLE_UPDATE', 'ROLE_DELETE', 'PERMISSION_ASSIGN', 'ANALYTICS_VIEW', 'REPORTS_GENERATE', 'REPORTS_EXPORT', 'SETTINGS_VIEW', 'SETTINGS_UPDATE');

-- CreateTable
CREATE TABLE "public"."customers" (
    "id" SERIAL NOT NULL,
    "firebase_uid" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "full_name" TEXT NOT NULL,
    "phone" TEXT,
    "addresses" JSONB,
    "preferred_currency" VARCHAR(3),
    "created" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "customers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Role" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "created" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Permission" (
    "id" SERIAL NOT NULL,
    "action" "public"."PermissionAction" NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "Permission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."role_permissions" (
    "role_id" INTEGER NOT NULL,
    "permission_id" INTEGER NOT NULL,

    CONSTRAINT "role_permissions_pkey" PRIMARY KEY ("role_id","permission_id")
);

-- CreateTable
CREATE TABLE "public"."customer_roles" (
    "customer_id" INTEGER NOT NULL,
    "role_id" INTEGER NOT NULL,

    CONSTRAINT "customer_roles_pkey" PRIMARY KEY ("customer_id","role_id")
);

-- CreateTable
CREATE TABLE "public"."products" (
    "id" SERIAL NOT NULL,
    "original_name" TEXT,
    "product_url" TEXT NOT NULL,
    "marketplace" "public"."Marketplace" NOT NULL DEFAULT 'alibaba',
    "can_show" BOOLEAN DEFAULT false,
    "created" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated" TIMESTAMPTZ NOT NULL,
    "weight" DECIMAL(65,30),
    "weight_unit" VARCHAR(10),

    CONSTRAINT "products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."product_translations" (
    "id" BIGSERIAL NOT NULL,
    "product_id" INTEGER NOT NULL,
    "language_code" VARCHAR(5) NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,

    CONSTRAINT "product_translations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."categories" (
    "id" SERIAL NOT NULL,
    "parent_id" INTEGER,
    "created" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."category_translations" (
    "id" BIGSERIAL NOT NULL,
    "category_id" INTEGER NOT NULL,
    "language_code" VARCHAR(5) NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "description" TEXT,

    CONSTRAINT "category_translations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."product_categories" (
    "product_id" INTEGER NOT NULL,
    "category_id" INTEGER NOT NULL,

    CONSTRAINT "product_categories_pkey" PRIMARY KEY ("product_id","category_id")
);

-- CreateTable
CREATE TABLE "public"."offers" (
    "id" BIGSERIAL NOT NULL,
    "product_id" INTEGER NOT NULL,
    "min_quantity" DECIMAL(65,30) NOT NULL,
    "price_low" DECIMAL(65,30) NOT NULL,
    "price_high" DECIMAL(65,30),
    "currency" VARCHAR(10) NOT NULL DEFAULT 'CNY',
    "quantity_info" TEXT,

    CONSTRAINT "offers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."variants" (
    "id" BIGSERIAL NOT NULL,
    "product_id" INTEGER NOT NULL,
    "original_variant_name" TEXT NOT NULL,
    "original_variant_type" TEXT NOT NULL,
    "available_quantity" DECIMAL(65,30),
    "min_quantity" DECIMAL(65,30),
    "price_low" DECIMAL(65,30) NOT NULL,
    "price_high" DECIMAL(65,30),
    "currency" VARCHAR(10) NOT NULL DEFAULT 'CNY',

    CONSTRAINT "variants_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."variant_translations" (
    "id" BIGSERIAL NOT NULL,
    "variant_id" BIGINT NOT NULL,
    "language_code" VARCHAR(5) NOT NULL,
    "variant_name" TEXT NOT NULL,
    "variant_type" TEXT NOT NULL,

    CONSTRAINT "variant_translations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."product_attributes" (
    "id" BIGSERIAL NOT NULL,
    "product_id" INTEGER NOT NULL,
    "original_attr_key" TEXT NOT NULL,
    "original_attr_value" TEXT NOT NULL,

    CONSTRAINT "product_attributes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."product_attributes_translations" (
    "id" BIGSERIAL NOT NULL,
    "attributes_id" BIGINT NOT NULL,
    "language_code" VARCHAR(5) NOT NULL,
    "attr_key" TEXT NOT NULL,
    "attr_value" TEXT NOT NULL,

    CONSTRAINT "product_attributes_translations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."product_images" (
    "id" BIGSERIAL NOT NULL,
    "product_id" INTEGER NOT NULL,
    "image_url" TEXT NOT NULL,
    "image_type" "public"."ProductImageType" NOT NULL,

    CONSTRAINT "product_images_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."custom_services" (
    "id" BIGSERIAL NOT NULL,
    "product_id" INTEGER NOT NULL,
    "original_service_name" TEXT,
    "original_service_type" TEXT,
    "min_quantity" DECIMAL(65,30),
    "price" DECIMAL(65,30),
    "currency" VARCHAR(10),

    CONSTRAINT "custom_services_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."custom_services_translations" (
    "id" BIGSERIAL NOT NULL,
    "custom_services_id" BIGINT NOT NULL,
    "language_code" VARCHAR(5) NOT NULL,
    "service_name" TEXT NOT NULL,
    "service_type" TEXT NOT NULL,

    CONSTRAINT "custom_services_translations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."orders" (
    "id" TEXT NOT NULL,
    "customer_id" INTEGER NOT NULL,
    "shipping_address" JSONB NOT NULL,
    "status" "public"."OrderStatus" NOT NULL DEFAULT 'pending',
    "total_amount" DECIMAL(12,2) NOT NULL,
    "currency" VARCHAR(3) NOT NULL,
    "exchange_rate" DECIMAL(10,6) NOT NULL,
    "shipping_cost" DECIMAL(12,2) NOT NULL,
    "created" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "orders_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."order_items" (
    "id" BIGSERIAL NOT NULL,
    "order_id" TEXT NOT NULL,
    "product_id" INTEGER NOT NULL,
    "variant_id" BIGINT,
    "quantity" DECIMAL(65,30) NOT NULL,
    "price_per_unit" DECIMAL(12,2) NOT NULL,
    "marketplace_order_id" TEXT,
    "marketplace_product_url" TEXT NOT NULL,
    "marketplace_notes" TEXT NOT NULL,

    CONSTRAINT "order_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."warehouse_receipts" (
    "id" TEXT NOT NULL,
    "order_item_id" BIGINT NOT NULL,
    "received_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "marketplace_order_id" TEXT NOT NULL,
    "package_weight" DECIMAL(65,30) NOT NULL,
    "package_weight_unit" VARCHAR(10) NOT NULL DEFAULT 'g',
    "status" "public"."WarehouseReceiptStatus" NOT NULL DEFAULT 'pending',
    "shipping_label_url" TEXT,
    "tracking_number" TEXT,
    "carrier" VARCHAR(50),

    CONSTRAINT "warehouse_receipts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."payments" (
    "id" TEXT NOT NULL,
    "order_id" TEXT NOT NULL,
    "amount" DECIMAL(12,2) NOT NULL,
    "currency" VARCHAR(3) NOT NULL,
    "payment_method" VARCHAR(50) NOT NULL,
    "transaction_id" TEXT NOT NULL,
    "status" "public"."PaymentStatus" NOT NULL,
    "created" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "payments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."user_activity" (
    "id" BIGSERIAL NOT NULL,
    "customer_id" INTEGER NOT NULL,
    "product_id" INTEGER NOT NULL,
    "activity_type" "public"."UserActivityType" NOT NULL,
    "created" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "session_id" TEXT NOT NULL,
    "marketplace" VARCHAR(20) NOT NULL,

    CONSTRAINT "user_activity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."product_similarity" (
    "product_id1" INTEGER NOT NULL,
    "product_id2" INTEGER NOT NULL,
    "similarity_score" DECIMAL(4,3) NOT NULL,
    "similarity_type" "public"."SimilarityType" NOT NULL,

    CONSTRAINT "product_similarity_pkey" PRIMARY KEY ("product_id1","product_id2","similarity_type")
);

-- CreateTable
CREATE TABLE "public"."pricing_rules" (
    "id" SERIAL NOT NULL,
    "rule_name" TEXT NOT NULL,
    "priority" INTEGER NOT NULL DEFAULT 0,
    "condition_type" VARCHAR(50) NOT NULL,
    "condition_value" TEXT,
    "markup_type" VARCHAR(50) NOT NULL,
    "markup_value" DECIMAL(10,4) NOT NULL,
    "is_active" BOOLEAN DEFAULT true,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "pricing_rules_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "customers_firebase_uid_key" ON "public"."customers"("firebase_uid");

-- CreateIndex
CREATE UNIQUE INDEX "customers_email_key" ON "public"."customers"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Role_name_key" ON "public"."Role"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Permission_action_key" ON "public"."Permission"("action");

-- CreateIndex
CREATE INDEX "role_permissions_permission_id_idx" ON "public"."role_permissions"("permission_id");

-- CreateIndex
CREATE INDEX "customer_roles_role_id_idx" ON "public"."customer_roles"("role_id");

-- CreateIndex
CREATE UNIQUE INDEX "products_product_url_key" ON "public"."products"("product_url");

-- CreateIndex
CREATE INDEX "products_marketplace_idx" ON "public"."products"("marketplace");

-- CreateIndex
CREATE INDEX "product_translations_language_code_idx" ON "public"."product_translations"("language_code");

-- CreateIndex
CREATE UNIQUE INDEX "product_translations_product_id_language_code_key" ON "public"."product_translations"("product_id", "language_code");

-- CreateIndex
CREATE UNIQUE INDEX "product_translations_language_code_slug_key" ON "public"."product_translations"("language_code", "slug");

-- CreateIndex
CREATE INDEX "categories_parent_id_idx" ON "public"."categories"("parent_id");

-- CreateIndex
CREATE UNIQUE INDEX "category_translations_category_id_language_code_key" ON "public"."category_translations"("category_id", "language_code");

-- CreateIndex
CREATE UNIQUE INDEX "category_translations_language_code_slug_key" ON "public"."category_translations"("language_code", "slug");

-- CreateIndex
CREATE INDEX "product_categories_category_id_idx" ON "public"."product_categories"("category_id");

-- CreateIndex
CREATE INDEX "offers_product_id_idx" ON "public"."offers"("product_id");

-- CreateIndex
CREATE INDEX "offers_price_low_idx" ON "public"."offers"("price_low");

-- CreateIndex
CREATE INDEX "variants_product_id_idx" ON "public"."variants"("product_id");

-- CreateIndex
CREATE INDEX "variants_original_variant_type_idx" ON "public"."variants"("original_variant_type");

-- CreateIndex
CREATE UNIQUE INDEX "variant_translations_variant_id_language_code_key" ON "public"."variant_translations"("variant_id", "language_code");

-- CreateIndex
CREATE INDEX "product_attributes_original_attr_key_original_attr_value_idx" ON "public"."product_attributes"("original_attr_key", "original_attr_value");

-- CreateIndex
CREATE UNIQUE INDEX "product_attributes_translations_attributes_id_language_code_key" ON "public"."product_attributes_translations"("attributes_id", "language_code");

-- CreateIndex
CREATE INDEX "product_images_product_id_image_type_idx" ON "public"."product_images"("product_id", "image_type");

-- CreateIndex
CREATE INDEX "custom_services_product_id_idx" ON "public"."custom_services"("product_id");

-- CreateIndex
CREATE UNIQUE INDEX "custom_services_translations_custom_services_id_language_co_key" ON "public"."custom_services_translations"("custom_services_id", "language_code");

-- CreateIndex
CREATE INDEX "orders_status_idx" ON "public"."orders"("status");

-- CreateIndex
CREATE INDEX "orders_customer_id_idx" ON "public"."orders"("customer_id");

-- CreateIndex
CREATE INDEX "order_items_marketplace_order_id_idx" ON "public"."order_items"("marketplace_order_id");

-- CreateIndex
CREATE INDEX "order_items_product_id_idx" ON "public"."order_items"("product_id");

-- CreateIndex
CREATE INDEX "warehouse_receipts_marketplace_order_id_idx" ON "public"."warehouse_receipts"("marketplace_order_id");

-- CreateIndex
CREATE INDEX "warehouse_receipts_status_idx" ON "public"."warehouse_receipts"("status");

-- CreateIndex
CREATE UNIQUE INDEX "payments_transaction_id_key" ON "public"."payments"("transaction_id");

-- CreateIndex
CREATE INDEX "payments_status_idx" ON "public"."payments"("status");

-- CreateIndex
CREATE INDEX "payments_order_id_idx" ON "public"."payments"("order_id");

-- CreateIndex
CREATE INDEX "user_activity_customer_id_idx" ON "public"."user_activity"("customer_id");

-- CreateIndex
CREATE INDEX "user_activity_product_id_idx" ON "public"."user_activity"("product_id");

-- CreateIndex
CREATE INDEX "user_activity_created_idx" ON "public"."user_activity"("created");

-- CreateIndex
CREATE INDEX "product_similarity_product_id1_idx" ON "public"."product_similarity"("product_id1");

-- CreateIndex
CREATE INDEX "pricing_rules_condition_type_condition_value_is_active_idx" ON "public"."pricing_rules"("condition_type", "condition_value", "is_active");

-- CreateIndex
CREATE UNIQUE INDEX "pricing_rules_condition_type_condition_value_key" ON "public"."pricing_rules"("condition_type", "condition_value");

-- AddForeignKey
ALTER TABLE "public"."role_permissions" ADD CONSTRAINT "role_permissions_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."Role"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."role_permissions" ADD CONSTRAINT "role_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "public"."Permission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."customer_roles" ADD CONSTRAINT "customer_roles_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."customer_roles" ADD CONSTRAINT "customer_roles_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."Role"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_translations" ADD CONSTRAINT "product_translations_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."categories" ADD CONSTRAINT "categories_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "public"."categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."category_translations" ADD CONSTRAINT "category_translations_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_categories" ADD CONSTRAINT "product_categories_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_categories" ADD CONSTRAINT "product_categories_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."offers" ADD CONSTRAINT "offers_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."variants" ADD CONSTRAINT "variants_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."variant_translations" ADD CONSTRAINT "variant_translations_variant_id_fkey" FOREIGN KEY ("variant_id") REFERENCES "public"."variants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_attributes" ADD CONSTRAINT "product_attributes_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_attributes_translations" ADD CONSTRAINT "product_attributes_translations_attributes_id_fkey" FOREIGN KEY ("attributes_id") REFERENCES "public"."product_attributes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_images" ADD CONSTRAINT "product_images_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."custom_services" ADD CONSTRAINT "custom_services_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."custom_services_translations" ADD CONSTRAINT "custom_services_translations_custom_services_id_fkey" FOREIGN KEY ("custom_services_id") REFERENCES "public"."custom_services"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."orders" ADD CONSTRAINT "orders_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."order_items" ADD CONSTRAINT "order_items_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."order_items" ADD CONSTRAINT "order_items_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."order_items" ADD CONSTRAINT "order_items_variant_id_fkey" FOREIGN KEY ("variant_id") REFERENCES "public"."variants"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."warehouse_receipts" ADD CONSTRAINT "warehouse_receipts_order_item_id_fkey" FOREIGN KEY ("order_item_id") REFERENCES "public"."order_items"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."payments" ADD CONSTRAINT "payments_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_activity" ADD CONSTRAINT "user_activity_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_activity" ADD CONSTRAINT "user_activity_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_similarity" ADD CONSTRAINT "product_similarity_product_id1_fkey" FOREIGN KEY ("product_id1") REFERENCES "public"."products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_similarity" ADD CONSTRAINT "product_similarity_product_id2_fkey" FOREIGN KEY ("product_id2") REFERENCES "public"."products"("id") ON DELETE CASCADE ON UPDATE CASCADE;
