'use client';

// components/admin/AdminHeader.tsx
// Admin header with user menu

import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { LogOut, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { auth } from '@/lib/firebase/client';
import { signOut } from 'firebase/auth';

interface AdminHeaderProps {
  locale: string;
  userName: string;
}

export function AdminHeader({ locale, userName }: AdminHeaderProps) {
  const t = useTranslations('admin');
  const router = useRouter();

  const handleLogout = async () => {
    try {
      // Sign out from Firebase
      await signOut(auth);

      // Clear session cookie
      await fetch('/api/auth/session', {
        method: 'DELETE',
      });

      // Redirect to login
      router.push(`/${locale}/login`);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <header className="bg-card border-b">
      <div className="flex items-center justify-between px-8 py-4">
        <div>
          <h1 className="text-2xl font-bold">{t('title')}</h1>
          <p className="text-sm text-muted-foreground">
            {t('welcome')}, {userName}
          </p>
        </div>

        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-sm">
            <User className="h-4 w-4" />
            <span>{userName}</span>
          </div>

          <Button variant="outline" size="sm" onClick={handleLogout}>
            <LogOut className="h-4 w-4 mr-2" />
            {t('logout')}
          </Button>
        </div>
      </div>
    </header>
  );
}

