'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { getCurrentUser } from '@/lib/actions/auth.actions';

interface CurrencyContextType {
  currency: string;
  setCurrency: (currency: string) => void;
  isLoading: boolean;
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined);

interface CurrencyProviderProps {
  children: React.ReactNode;
  defaultCurrency?: string;
}

export function CurrencyProvider({ children, defaultCurrency = 'USD' }: CurrencyProviderProps) {
  const [currency, setCurrencyState] = useState(defaultCurrency);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const initializeCurrency = async () => {
      try {
        // Get user preference from localStorage first (client-side)
        const stored = localStorage.getItem('preferred_currency');
        if (stored) {
          setCurrencyState(stored);
          setIsLoading(false);
          return;
        }

        // Try to get from user profile (server-side)
        const user = await getCurrentUser();
        if (user?.preferredCurrency) {
          setCurrencyState(user.preferredCurrency);
          localStorage.setItem('preferred_currency', user.preferredCurrency);
        } else {
          // Fallback to browser locale detection
          const browserCurrency = getCurrencyFromLocale();
          setCurrencyState(browserCurrency);
        }
      } catch (error) {
        console.error('Error initializing currency:', error);
        setCurrencyState(defaultCurrency);
      } finally {
        setIsLoading(false);
      }
    };

    initializeCurrency();
  }, [defaultCurrency]);

  const setCurrency = async (newCurrency: string) => {
    setCurrencyState(newCurrency);
    localStorage.setItem('preferred_currency', newCurrency);

    // Optionally update user preference in database
    try {
      const user = await getCurrentUser();
      if (user) {
        await fetch('/api/user/currency', {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ currency: newCurrency }),
        });
      }
    } catch (error) {
      console.error('Error updating user currency:', error);
    }

    // Refresh the page to update all prices
    router.refresh();
  };

  return (
    <CurrencyContext.Provider value={{ currency, setCurrency, isLoading }}>
      {children}
    </CurrencyContext.Provider>
  );
}

export function useCurrency() {
  const context = useContext(CurrencyContext);
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
}

// Helper function to detect currency from browser locale
function getCurrencyFromLocale(): string {
  if (typeof window === 'undefined') return 'USD';

  const locale = navigator.language || 'en-US';
  const currencyMap: Record<string, string> = {
    'en-US': 'USD',
    'en-GB': 'GBP',
    'en-EU': 'EUR',
    'ja-JP': 'JPY',
    'zh-CN': 'CNY',
    'fr-FR': 'EUR',
    'de-DE': 'EUR',
    'es-ES': 'EUR',
    'it-IT': 'EUR',
    // Add more mappings as needed
  };

  // Check exact match first
  if (currencyMap[locale]) {
    return currencyMap[locale];
  }

  // Check language prefix
  const language = locale.split('-')[0];
  const languageMap: Record<string, string> = {
    'en': 'USD',
    'fr': 'EUR',
    'de': 'EUR',
    'es': 'EUR',
    'it': 'EUR',
    'ja': 'JPY',
    'zh': 'CNY',
  };

  return languageMap[language] || 'USD';
}