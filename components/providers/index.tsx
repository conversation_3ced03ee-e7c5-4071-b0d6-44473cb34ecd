'use client';

// components/providers/index.tsx
// Compose all providers together

import { ThemeProvider } from './ThemeProvider';
import { FirebaseAuthProvider } from './FirebaseAuthProvider';
import { CurrencyProvider } from './CurrencyProvider';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider>
      <FirebaseAuthProvider>
        <CurrencyProvider>
          {children}
        </CurrencyProvider>
      </FirebaseAuthProvider>
    </ThemeProvider>
  );
}
