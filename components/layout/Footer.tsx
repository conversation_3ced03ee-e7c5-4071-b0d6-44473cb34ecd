'use client';

// components/layout/Footer.tsx
// Footer with links and information

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Separator } from '@/components/ui/separator';

export function Footer() {
  const t = useTranslations('footer');
  const pathname = usePathname();
  const locale = pathname.split('/')[1] || 'en';

  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">🐱</span>
              <span className="text-xl font-bold">MaoMao</span>
            </div>
            <p className="text-sm text-muted-foreground">
              {t('description')}
            </p>
          </div>

          {/* Company */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">{t('about')}</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  href={`/${locale}/about`}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  {t('about')}
                </Link>
              </li>
              <li>
                <Link
                  href={`/${locale}/contact`}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  {t('contact')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">{t('support')}</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  href={`/${locale}/faq`}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  {t('faq')}
                </Link>
              </li>
              <li>
                <Link
                  href={`/${locale}/support`}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  {t('support')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">Legal</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  href={`/${locale}/terms`}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  {t('terms')}
                </Link>
              </li>
              <li>
                <Link
                  href={`/${locale}/privacy`}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  {t('privacy')}
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <Separator className="my-8" />

        <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
          <p className="text-sm text-muted-foreground">
            © {new Date().getFullYear()} MaoMao. All rights reserved.
          </p>
          <div className="flex space-x-4">
            {/* Language Selector */}
            <div className="flex space-x-2">
              <Link
                href={`/en${pathname.slice(3)}`}
                className={`text-sm ${
                  locale === 'en' ? 'font-semibold' : 'text-muted-foreground'
                }`}
              >
                EN
              </Link>
              <span className="text-muted-foreground">|</span>
              <Link
                href={`/fr${pathname.slice(3)}`}
                className={`text-sm ${
                  locale === 'fr' ? 'font-semibold' : 'text-muted-foreground'
                }`}
              >
                FR
              </Link>
              <span className="text-muted-foreground">|</span>
              <Link
                href={`/ar${pathname.slice(3)}`}
                className={`text-sm ${
                  locale === 'ar' ? 'font-semibold' : 'text-muted-foreground'
                }`}
              >
                AR
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}

