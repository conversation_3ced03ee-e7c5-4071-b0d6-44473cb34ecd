// lib/actions/admin/order.actions.ts
// Admin order management server actions

'use server';

import { revalidatePath } from 'next/cache';
import { Prisma } from '@/app/generated/prisma';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction, OrderStatus } from '@/app/generated/prisma';
import { ORDERS_PER_PAGE } from '@/lib/constants';
import type {
  OrderAdminListItem,
  DashboardStats,
  ErrorResponse,
  SuccessResponse,
  AdminPaginatedResponse,
} from '@/lib/types/admin';
import {
  buildCursorWhereClause,
  buildCursorOrderBy,
  applyCursorPagination
} from '@/lib/utils/cursor-pagination';

/**
 * Get all orders with filters (admin)
 */
export async function getAllOrders(filters: {
  search?: string;
  status?: OrderStatus;
  cursor?: string;
  limit?: number;
} = {}): Promise<AdminPaginatedResponse<OrderAdminListItem> | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.ORDER_READ_ALL
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    const { search, status, cursor, limit = ORDERS_PER_PAGE } = filters;

    // Build where clause
    const where: Prisma.ordersWhereInput = {
      ...buildCursorWhereClause(cursor, 'created', 'id'),
    };

    if (status) {
      where.status = status;
    }

    if (search) {
      where.OR = [
        { id: { contains: search, mode: 'insensitive' } },
        {
          customer: {
            OR: [
              { email: { contains: search, mode: 'insensitive' } },
              { full_name: { contains: search, mode: 'insensitive' } },
            ],
          },
        },
      ];
    }

    // Fetch orders with cursor pagination
    // Using select for optimal performance - only fetch needed fields
    const orders = await prisma.orders.findMany({
        where,
        select: {
          id: true,
          status: true,
          total_amount: true,
          currency: true,
          created: true,
          customer: {
            select: {
              id: true,
              email: true,
              full_name: true,
            },
          },
          order_items: {
            take: 3,
            select: {
              id: true,
              quantity: true,
              price_per_unit: true,
            },
          },
          _count: {
            select: {
              order_items: true,
            },
          },
        },
        orderBy: buildCursorOrderBy('created', 'id'),
        take: limit + 1, // Fetch one extra to determine hasMore
      });

    // Apply cursor pagination logic
    const paginationResult = applyCursorPagination(orders, {
      cursor,
      limit,
      timestampField: 'created',
      idField: 'id',
    });

    return {
      data: paginationResult.data,
      hasMore: paginationResult.hasMore,
      nextCursor: paginationResult.nextCursor,
      prevCursor: paginationResult.prevCursor,
    };
  } catch (error) {
    console.error('Error fetching orders:', error);
    return { error: 'Failed to fetch orders' };
  }
}

/**
 * Update order status (admin)
 */
export async function updateOrderStatus(
  orderId: string,
  status: OrderStatus,
  trackingNumber?: string
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.ORDER_UPDATE_STATUS
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Update order
    await prisma.orders.update({
      where: { id: orderId },
      data: {
        status,
        updated: new Date(),
      },
    });

    revalidatePath('/admin/orders');
    revalidatePath(`/admin/orders/${orderId}`);
    revalidatePath('/account/orders');

    return { success: true };
  } catch (error) {
    console.error('Error updating order status:', error);
    return { success: false, error: 'Failed to update order status' };
  }
}

/**
 * Get dashboard statistics (admin)
 */
export async function getDashboardStats(): Promise<DashboardStats | ErrorResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { error: 'Not authenticated' };
    }

    // Check permission - requires VIEW_DASHBOARD_STATS permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.VIEW_DASHBOARD_STATS
    );

    if (!hasPermission) {
      return { error: 'Insufficient permissions' };
    }

    // Fetch stats in parallel
    const [totalOrders, pendingOrders, processingOrders, recentOrders, revenueData] =
      await Promise.all([
        prisma.orders.count(),
        prisma.orders.count({ where: { status: OrderStatus.pending } }),
        prisma.orders.count({ where: { status: OrderStatus.processing } }),
        prisma.orders.findMany({
          take: 10,
          orderBy: { created: 'desc' },
          include: {
            customer: {
              select: {
                id: true,
                email: true,
                full_name: true,
              },
            },
          },
        }),
        prisma.orders.aggregate({
          _sum: {
            total_amount: true,
          },
        }),
      ]);

    const totalRevenue = Number(revenueData._sum.total_amount || 0);

    return {
      totalRevenue,
      totalOrders,
      pendingOrders,
      processingOrders,
      recentOrders,
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return { error: 'Failed to fetch dashboard stats' };
  }
}

/**
 * Update marketplace order ID for order item
 */
export async function updateMarketplaceOrderId(
  orderItemId: number,
  marketplaceOrderId: string,
  notes?: string
): Promise<SuccessResponse> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check permission
    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.ORDER_UPDATE_MARKETPLACE_INFO
    );

    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }

    // Update order item
    await prisma.order_items.update({
      where: { id: BigInt(orderItemId) },
      data: {
        marketplace_order_id: marketplaceOrderId,
        marketplace_notes: notes || '',
      },
    });

    revalidatePath('/admin/orders');

    return {
      success: true,
      message: 'Marketplace order ID updated successfully',
    };
  } catch (error) {
    console.error('Error updating marketplace order ID:', error);
    return { success: false, error: 'Failed to update marketplace order ID' };
  }
}
