'use server';

// lib/actions/order.actions.ts
// Server actions for order management

import { Prisma } from '@/app/generated/prisma';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from './auth.actions';
import { revalidatePath } from 'next/cache';
import type { CartItem, Address } from '@/lib/types';
import { ORDERS_PER_PAGE } from '@/lib/constants';
import {
  buildCursorWhereClause,
  buildCursorOrderBy,
  applyCursorPagination
} from '@/lib/utils/cursor-pagination';

/**
 * Create a new order from cart items
 */
export async function createOrder(data: {
  items: CartItem[];
  shippingAddress: Address;
  currency: string;
  exchangeRate: number;
  shippingCost: number;
}) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        error: 'Not authenticated',
      };
    }

    const { items, shippingAddress, currency, exchangeRate, shippingCost } = data;

    if (!items.length) {
      return {
        success: false,
        error: 'Cart is empty',
      };
    }

    // Calculate total
    const subtotal = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const totalAmount = subtotal + shippingCost;

    // Create order with order items in a transaction
    const order = await prisma.$transaction(async (tx) => {
      // Create order
      const newOrder = await tx.orders.create({
        data: {
          customer_id: user.customerId,
          shipping_address: shippingAddress as unknown as Prisma.InputJsonValue,
          status: 'pending',
          total_amount: totalAmount,
          currency: currency,
          exchange_rate: exchangeRate,
          shipping_cost: shippingCost,
        },
      });

      // Create order items
      for (const item of items) {
        // Get product details for marketplace info
        const product = await tx.products.findUnique({
          where: { id: item.productId },
          select: {
            product_url: true,
            marketplace: true,
          },
        });

        if (!product) continue;

        await tx.order_items.create({
          data: {
            order_id: newOrder.id,
            product_id: item.productId,
            variant_id: item.variantId || null,
            quantity: item.quantity,
            price_per_unit: item.price,
            marketplace_product_url: product.product_url,
            marketplace_notes: `Marketplace: ${product.marketplace}`,
          },
        });
      }

      return newOrder;
    });

    revalidatePath('/account/orders');

    return {
      success: true,
      orderId: order.id,
    };
  } catch (error) {
    console.error('Error creating order:', error);
    return {
      success: false,
      error: 'Failed to create order',
    };
  }
}

/**
 * Get user orders with cursor-based pagination
 */
export async function getUserOrders(cursor?: string, limit: number = ORDERS_PER_PAGE) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return null;
    }

    const orders = await prisma.orders.findMany({
        where: {
          customer_id: user.customerId,
          ...buildCursorWhereClause(cursor, 'created', 'id'),
        },
        orderBy: buildCursorOrderBy('created', 'id'),
        take: limit + 1, // Fetch one extra to determine hasMore
        include: {
          order_items: {
            take: 5, // Limit items shown in list
            include: {
              product: {
                include: {
                  translations: {
                    take: 1,
                  },
                },
              },
            },
          },
        },
      });

    // Apply cursor pagination logic
    const paginationResult = applyCursorPagination(orders, {
      cursor,
      limit,
      timestampField: 'created',
      idField: 'id',
    });

    return {
      orders: paginationResult.data,
      pagination: {
        limit,
        hasMore: paginationResult.hasMore,
        nextCursor: paginationResult.nextCursor,
        prevCursor: paginationResult.prevCursor,
      },
    };
  } catch (error) {
    console.error('Error fetching orders:', error);
    return null;
  }
}

/**
 * Get single order by ID
 * Only returns if order belongs to current user
 */
export async function getOrderById(orderId: string) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return null;
    }

    const order = await prisma.orders.findFirst({
      where: {
        id: orderId,
        customer_id: user.customerId, // Security: only user's own orders
      },
      include: {
        order_items: {
          include: {
            product: {
              include: {
                translations: {
                  take: 1,
                },
                product_images: {
                  where: {
                    image_type: 'preview',
                  },
                  take: 1,
                },
              },
            },
            variant: {
              include: {
                translations: {
                  take: 1,
                },
              },
            },
          },
        },
        payments: true,
      },
    });

    return order;
  } catch (error) {
    console.error('Error fetching order:', error);
    return null;
  }
}
