// lib/constants.ts
// Application-wide constants

// Site metadata
export const SITE_CONFIG = {
  name: '<PERSON><PERSON><PERSON>',
  description: 'Your gateway to Chinese marketplaces - Taobao, Pinduoduo, and Alibaba',
  url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
} as const;

// Supported locales
export const LOCALES = ['en', 'fr', 'ar'] as const;
export const DEFAULT_LOCALE = 'en' as const;

// Supported currencies
export const CURRENCIES = {
  USD: { symbol: '$', name: 'US Dollar' },
  EUR: { symbol: '€', name: 'Euro' },
  GBP: { symbol: '£', name: 'British Pound' },
  CNY: { symbol: '¥', name: 'Chinese Yuan' },
  CAD: { symbol: 'C$', name: 'Canadian Dollar' },
  AUD: { symbol: 'A$', name: 'Australian Dollar' },
} as const;

export const DEFAULT_CURRENCY = 'USD' as const;

// Cursor Pagination - Default limits
export const PRODUCTS_PER_PAGE = 24;
export const ORDERS_PER_PAGE = 10;
export const MAX_PRODUCT_IMAGES_DISPLAY = 10; // Limit images shown per product

// Product filters
export const PRICE_RANGES = [
  { label: 'Under $25', min: 0, max: 25 },
  { label: '$25 - $50', min: 25, max: 50 },
  { label: '$50 - $100', min: 50, max: 100 },
  { label: '$100 - $200', min: 100, max: 200 },
  { label: 'Over $200', min: 200, max: null },
] as const;

// Marketplaces
export const MARKETPLACES = {
  taobao: { name: 'Taobao', icon: '🛍️' },
  pinduoduo: { name: 'Pinduoduo', icon: '🛒' },
  alibaba: { name: 'Alibaba', icon: '🏪' },
} as const;

// Order statuses with display info
export const ORDER_STATUS_CONFIG = {
  pending: {
    label: 'Pending Payment',
    color: 'yellow',
    description: 'Awaiting payment confirmation',
  },
  processing: {
    label: 'Processing',
    color: 'blue',
    description: 'Order is being processed',
  },
  shipped: {
    label: 'Shipped',
    color: 'purple',
    description: 'Order has been shipped',
  },
  delivered: {
    label: 'Delivered',
    color: 'green',
    description: 'Order has been delivered',
  },
  cancelled: {
    label: 'Cancelled',
    color: 'red',
    description: 'Order has been cancelled',
  },
  refunded: {
    label: 'Refunded',
    color: 'gray',
    description: 'Order has been refunded',
  },
} as const;

// Routes
export const ROUTES = {
  HOME: '/',
  PRODUCTS: '/products',
  CART: '/cart',
  LOGIN: '/login',
  REGISTER: '/register',
  ACCOUNT: '/account',
  ACCOUNT_PROFILE: '/account/profile',
  ACCOUNT_ORDERS: '/account/orders',
  CHECKOUT_SHIPPING: '/checkout/shipping',
  CHECKOUT_PAYMENT: '/checkout/payment',
  ADMIN: '/admin',
} as const;

// Protected routes (require authentication)
export const PROTECTED_ROUTES = [
  '/account',
  '/checkout',
] as const;

// Public routes (redirect to account if authenticated)
export const AUTH_ROUTES = [
  '/login',
  '/register',
] as const;

// ID token cookie name
export const ID_TOKEN_COOKIE_NAME = '__session';
export const ID_TOKEN_MAX_AGE = 60 * 60; // 1 hour in seconds

// Cart storage key
export const CART_STORAGE_KEY = 'maomao-cart';

// Image optimization
export const IMAGE_SIZES = {
  thumbnail: 150,
  card: 300,
  detail: 800,
  full: 1200,
} as const;
