// lib/services/pricing.ts
// Pricing calculation service

import { prisma } from '@/lib/prisma';
import { exchangeRateService } from './exchange-rate';
import type { pricing_rules } from '@/app/generated/prisma';

interface PricingContext {
  productId?: number;
  categoryId?: number;
  marketplace?: string;
  userCurrency: string;
}

interface PricingResult {
  basePrice: number; // Cost price in CNY
  sellingPrice: number; // Base selling price after markup in CNY
  displayPrice: number; // Converted price in user's currency
  currency: string;
  exchangeRate: number;
  appliedRules: Array<{
    id: number;
    name: string;
    markup: number;
  }>;
}

export class PricingService {
  async calculatePrice(
    costPriceCNY: number,
    context: PricingContext
  ): Promise<PricingResult> {
    // Get applicable pricing rules
    const rules = await this.getApplicableRules(context);

    // Apply markups in priority order
    let sellingPrice = costPriceCNY;
    const appliedRules: PricingResult['appliedRules'] = [];

    for (const rule of rules) {
      const markupAmount = this.calculateMarkup(sellingPrice, rule);
      sellingPrice += markupAmount;
      appliedRules.push({
        id: rule.id,
        name: rule.rule_name,
        markup: markupAmount,
      });
    }

    // Convert to user's currency
    const exchangeRate = await exchangeRateService.getExchangeRate('CNY', context.userCurrency);
    const displayPrice = sellingPrice * exchangeRate;

    // Round to 2 decimal places
    const roundedDisplayPrice = Math.round(displayPrice * 100) / 100;

    return {
      basePrice: costPriceCNY,
      sellingPrice,
      displayPrice: roundedDisplayPrice,
      currency: context.userCurrency,
      exchangeRate,
      appliedRules,
    };
  }

  private async getApplicableRules(context: PricingContext) {
    const where: {
      is_active: boolean;
      OR?: Array<{
        condition_type: string;
        condition_value?: string;
      }>;
    } = {
      is_active: true,
    };

    // Build conditions
    const conditions: Array<{
      condition_type: string;
      condition_value?: string;
    }> = [];

    // Global rules
    conditions.push({ condition_type: 'GLOBAL' });

    // Marketplace-specific
    if (context.marketplace) {
      conditions.push({ condition_type: 'MARKETPLACE', condition_value: context.marketplace });
    }

    // Category-specific
    if (context.categoryId) {
      conditions.push({ condition_type: 'CATEGORY', condition_value: context.categoryId.toString() });
    }

    // Product-specific
    if (context.productId) {
      conditions.push({ condition_type: 'PRODUCT_ID', condition_value: context.productId.toString() });
    }

    where.OR = conditions;

    const rules = await prisma.pricing_rules.findMany({
      where,
      orderBy: { priority: 'asc' }, // Lower priority number = higher priority
    });

    return rules;
  }

  private calculateMarkup(currentPrice: number, rule: pricing_rules): number {
    const { markup_type, markup_value } = rule;
    const value = Number(markup_value);

    switch (markup_type) {
      case 'PERCENTAGE':
        return currentPrice * (value / 100);
      case 'FIXED_AMOUNT_ADD':
        return value;
      case 'FIXED_AMOUNT_SET':
        return value - currentPrice;
      default:
        return 0;
    }
  }

  // Get user's currency based on priority
  async getUserCurrency(userId?: string, request?: Request): Promise<string> {
    // For server-side, we can't easily get user preferences without authentication
    // In a real app, you'd pass the currency from client-side context
    // For now, default to USD
    return 'USD';
  }

  private getClientIP(request: Request): string {
    // Get IP from headers (simplified)
    const forwarded = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    return forwarded?.split(',')[0] || realIP || '127.0.0.1';
  }

  private async getCountryFromIP(ip: string): Promise<string | null> {
    // In real implementation, use a geo IP service
    // For demo, return null to fall back to default
    return null;
  }

  private getCurrencyFromCountry(country: string): string | null {
    // Simplified country to currency mapping
    const mapping: Record<string, string> = {
      'US': 'USD',
      'GB': 'GBP',
      'EU': 'EUR',
      'JP': 'JPY',
      'CN': 'CNY',
      // Add more as needed
    };
    return mapping[country] || null;
  }
}

export const pricingService = new PricingService();