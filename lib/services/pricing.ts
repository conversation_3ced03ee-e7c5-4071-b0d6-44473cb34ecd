// lib/services/pricing.ts
// Pricing calculation service with batch processing and rule caching

import { prisma } from '@/lib/prisma';
import { exchangeRateService } from './exchange-rate';
import type { pricing_rules } from '@/app/generated/prisma';

interface PricingContext {
  productId?: number;
  categoryId?: number;
  marketplace?: string;
  userCurrency: string;
}

interface PricingResult {
  basePrice: number; // Cost price in CNY
  sellingPrice: number; // Base selling price after markup in CNY
  displayPrice: number; // Converted price in user's currency
  currency: string;
  exchangeRate: number;
  appliedRules: Array<{
    id: number;
    name: string;
    markup: number;
  }>;
}

interface BatchPricingInput {
  costPriceCNY: number;
  context: PricingContext;
}

interface BatchPricingResult extends PricingResult {
  productId?: number;
}

// Rule cache to avoid duplicate database queries
interface RuleCache {
  [key: string]: pricing_rules[];
}

// Cache for exchange rates to avoid repeated API calls
interface ExchangeRateCache {
  [key: string]: {
    rate: number;
    timestamp: number;
  };
}

export class PricingService {
  private ruleCache: RuleCache = {};
  private exchangeRateCache: ExchangeRateCache = {};
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  async calculatePrice(
    costPriceCNY: number,
    context: PricingContext
  ): Promise<PricingResult> {
    // Get applicable pricing rules
    const rules = await this.getApplicableRules(context);

    // Apply markups in priority order
    let sellingPrice = costPriceCNY;
    const appliedRules: PricingResult['appliedRules'] = [];

    for (const rule of rules) {
      const markupAmount = this.calculateMarkup(sellingPrice, rule);
      sellingPrice += markupAmount;
      appliedRules.push({
        id: rule.id,
        name: rule.rule_name,
        markup: markupAmount,
      });
    }

    // Convert to user's currency
    const exchangeRate = await this.getCachedExchangeRate('CNY', context.userCurrency);
    const displayPrice = sellingPrice * exchangeRate;

    // Round to 2 decimal places
    const roundedDisplayPrice = Math.round(displayPrice * 100) / 100;

    return {
      basePrice: costPriceCNY,
      sellingPrice,
      displayPrice: roundedDisplayPrice,
      currency: context.userCurrency,
      exchangeRate,
      appliedRules,
    };
  }

  /**
   * Batch calculate prices for multiple products
   * Optimized to reduce database queries by caching rules and exchange rates
   */
  async calculatePrices(
    inputs: BatchPricingInput[]
  ): Promise<BatchPricingResult[]> {
    if (inputs.length === 0) return [];

    // Group inputs by unique rule contexts to minimize database queries
    const contextGroups = this.groupByRuleContext(inputs);

    // Pre-fetch all unique rule sets
    await this.prefetchRules(contextGroups);

    // Get unique currencies and pre-fetch exchange rates
    const uniqueCurrencies = [...new Set(inputs.map(input => input.context.userCurrency))];
    await this.prefetchExchangeRates(uniqueCurrencies);

    // Calculate prices for all inputs
    const results: BatchPricingResult[] = [];

    for (const input of inputs) {
      const result = await this.calculatePriceFromCache(input.costPriceCNY, input.context);
      results.push({
        ...result,
        productId: input.context.productId,
      });
    }

    return results;
  }

  /**
   * Get applicable rules with caching
   */
  private async getApplicableRules(context: PricingContext): Promise<pricing_rules[]> {
    const cacheKey = this.getRuleCacheKey(context);

    // Check cache first
    if (this.ruleCache[cacheKey]) {
      return this.ruleCache[cacheKey];
    }

    const rules = await this.fetchRulesFromDatabase(context);

    // Cache the result
    this.ruleCache[cacheKey] = rules;

    return rules;
  }

  /**
   * Fetch rules from database
   */
  private async fetchRulesFromDatabase(context: PricingContext): Promise<pricing_rules[]> {
    const where: {
      is_active: boolean;
      OR?: Array<{
        condition_type: string;
        condition_value?: string;
      }>;
    } = {
      is_active: true,
    };

    // Build conditions
    const conditions: Array<{
      condition_type: string;
      condition_value?: string;
    }> = [];

    // Global rules
    conditions.push({ condition_type: 'GLOBAL' });

    // Marketplace-specific
    if (context.marketplace) {
      conditions.push({ condition_type: 'MARKETPLACE', condition_value: context.marketplace });
    }

    // Category-specific
    if (context.categoryId) {
      conditions.push({ condition_type: 'CATEGORY', condition_value: context.categoryId.toString() });
    }

    // Product-specific
    if (context.productId) {
      conditions.push({ condition_type: 'PRODUCT_ID', condition_value: context.productId.toString() });
    }

    where.OR = conditions;

    const rules = await prisma.pricing_rules.findMany({
      where,
      orderBy: { priority: 'asc' }, // Lower priority number = higher priority
    });

    return rules;
  }

  private calculateMarkup(currentPrice: number, rule: pricing_rules): number {
    const { markup_type, markup_value } = rule;
    const value = Number(markup_value);

    switch (markup_type) {
      case 'PERCENTAGE':
        return currentPrice * (value / 100);
      case 'FIXED_AMOUNT_ADD':
        return value;
      case 'FIXED_AMOUNT_SET':
        return value - currentPrice;
      default:
        return 0;
    }
  }

  /**
   * Calculate price using cached rules and exchange rates
   */
  private async calculatePriceFromCache(
    costPriceCNY: number,
    context: PricingContext
  ): Promise<PricingResult> {
    // Get rules from cache
    const rules = await this.getApplicableRules(context);

    // Apply markups in priority order
    let sellingPrice = costPriceCNY;
    const appliedRules: PricingResult['appliedRules'] = [];

    for (const rule of rules) {
      const markupAmount = this.calculateMarkup(sellingPrice, rule);
      sellingPrice += markupAmount;
      appliedRules.push({
        id: rule.id,
        name: rule.rule_name,
        markup: markupAmount,
      });
    }

    // Get exchange rate from cache
    const exchangeRate = await this.getCachedExchangeRate('CNY', context.userCurrency);
    const displayPrice = sellingPrice * exchangeRate;

    // Round to 2 decimal places
    const roundedDisplayPrice = Math.round(displayPrice * 100) / 100;

    return {
      basePrice: costPriceCNY,
      sellingPrice,
      displayPrice: roundedDisplayPrice,
      currency: context.userCurrency,
      exchangeRate,
      appliedRules,
    };
  }

  /**
   * Group inputs by rule context to minimize database queries
   */
  private groupByRuleContext(inputs: BatchPricingInput[]): Set<string> {
    const contextGroups = new Set<string>();

    for (const input of inputs) {
      const cacheKey = this.getRuleCacheKey(input.context);
      contextGroups.add(cacheKey);
    }

    return contextGroups;
  }

  /**
   * Pre-fetch rules for all unique contexts
   */
  private async prefetchRules(contextGroups: Set<string>): Promise<void> {
    const promises: Promise<void>[] = [];

    for (const cacheKey of contextGroups) {
      if (!this.ruleCache[cacheKey]) {
        const context = this.parseRuleCacheKey(cacheKey);
        promises.push(
          this.fetchRulesFromDatabase(context).then(rules => {
            this.ruleCache[cacheKey] = rules;
          })
        );
      }
    }

    await Promise.all(promises);
  }

  /**
   * Pre-fetch exchange rates for all unique currencies
   */
  private async prefetchExchangeRates(currencies: string[]): Promise<void> {
    const promises: Promise<void>[] = [];

    for (const currency of currencies) {
      if (currency !== 'CNY') {
        const cacheKey = `CNY_${currency}`;
        const cached = this.exchangeRateCache[cacheKey];

        if (!cached || Date.now() - cached.timestamp > this.CACHE_TTL) {
          promises.push(
            exchangeRateService.getExchangeRate('CNY', currency).then(rate => {
              this.exchangeRateCache[cacheKey] = {
                rate,
                timestamp: Date.now(),
              };
            })
          );
        }
      }
    }

    await Promise.all(promises);
  }

  /**
   * Get cached exchange rate
   */
  private async getCachedExchangeRate(fromCurrency: string, toCurrency: string): Promise<number> {
    if (fromCurrency === toCurrency) return 1;

    const cacheKey = `${fromCurrency}_${toCurrency}`;
    const cached = this.exchangeRateCache[cacheKey];

    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.rate;
    }

    // Fallback to direct API call if not cached
    const rate = await exchangeRateService.getExchangeRate(fromCurrency, toCurrency);
    this.exchangeRateCache[cacheKey] = {
      rate,
      timestamp: Date.now(),
    };

    return rate;
  }

  /**
   * Generate cache key for rule context
   */
  private getRuleCacheKey(context: PricingContext): string {
    return `${context.marketplace || 'null'}_${context.categoryId || 'null'}_${context.productId || 'null'}`;
  }

  /**
   * Parse cache key back to context (for prefetching)
   */
  private parseRuleCacheKey(cacheKey: string): PricingContext {
    const [marketplace, categoryId, productId] = cacheKey.split('_');

    return {
      marketplace: marketplace === 'null' ? undefined : marketplace,
      categoryId: categoryId === 'null' ? undefined : parseInt(categoryId),
      productId: productId === 'null' ? undefined : parseInt(productId),
      userCurrency: 'USD', // This will be overridden in actual usage
    };
  }

  // Get user's currency based on priority
  async getUserCurrency(_userId?: string, _request?: Request): Promise<string> {
    // For server-side, we can't easily get user preferences without authentication
    // In a real app, you'd pass the currency from client-side context
    // For now, default to USD
    return 'USD';
  }

  private getClientIP(request: Request): string {
    // Get IP from headers (simplified)
    const forwarded = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    return forwarded?.split(',')[0] || realIP || '127.0.0.1';
  }

  private async getCountryFromIP(_ip: string): Promise<string | null> {
    // In real implementation, use a geo IP service
    // For demo, return null to fall back to default
    return null;
  }

  private getCurrencyFromCountry(country: string): string | null {
    // Simplified country to currency mapping
    const mapping: Record<string, string> = {
      'US': 'USD',
      'GB': 'GBP',
      'EU': 'EUR',
      'JP': 'JPY',
      'CN': 'CNY',
      // Add more as needed
    };
    return mapping[country] || null;
  }
}

export const pricingService = new PricingService();