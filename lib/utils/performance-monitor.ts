// lib/utils/performance-monitor.ts
// Performance monitoring utilities for tracking optimization effectiveness

interface PerformanceMetric {
  operation: string;
  duration: number;
  queryCount?: number;
  cacheHits?: number;
  cacheMisses?: number;
  timestamp: Date;
}

interface QueryTracker {
  count: number;
  queries: string[];
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private queryTracker: QueryTracker = { count: 0, queries: [] };
  private cacheStats = { hits: 0, misses: 0 };

  /**
   * Start timing an operation
   */
  startTimer(operation: string): () => PerformanceMetric {
    const startTime = performance.now();
    const initialQueryCount = this.queryTracker.count;
    const initialCacheHits = this.cacheStats.hits;
    const initialCacheMisses = this.cacheStats.misses;

    return () => {
      const duration = performance.now() - startTime;
      const queryCount = this.queryTracker.count - initialQueryCount;
      const cacheHits = this.cacheStats.hits - initialCacheHits;
      const cacheMisses = this.cacheStats.misses - initialCacheMisses;

      const metric: PerformanceMetric = {
        operation,
        duration,
        queryCount,
        cacheHits,
        cacheMisses,
        timestamp: new Date(),
      };

      this.metrics.push(metric);
      return metric;
    };
  }

  /**
   * Track database query
   */
  trackQuery(query: string): void {
    this.queryTracker.count++;
    this.queryTracker.queries.push(query);
  }

  /**
   * Track cache hit
   */
  trackCacheHit(): void {
    this.cacheStats.hits++;
  }

  /**
   * Track cache miss
   */
  trackCacheMiss(): void {
    this.cacheStats.misses++;
  }

  /**
   * Get performance summary
   */
  getSummary(): {
    totalOperations: number;
    averageDuration: number;
    totalQueries: number;
    cacheHitRate: number;
    recentMetrics: PerformanceMetric[];
  } {
    const totalOperations = this.metrics.length;
    const averageDuration = totalOperations > 0 
      ? this.metrics.reduce((sum, m) => sum + m.duration, 0) / totalOperations 
      : 0;
    
    const totalQueries = this.metrics.reduce((sum, m) => sum + (m.queryCount || 0), 0);
    const totalCacheOperations = this.cacheStats.hits + this.cacheStats.misses;
    const cacheHitRate = totalCacheOperations > 0 
      ? (this.cacheStats.hits / totalCacheOperations) * 100 
      : 0;

    return {
      totalOperations,
      averageDuration,
      totalQueries,
      cacheHitRate,
      recentMetrics: this.metrics.slice(-10), // Last 10 operations
    };
  }

  /**
   * Get metrics for specific operation
   */
  getOperationMetrics(operation: string): PerformanceMetric[] {
    return this.metrics.filter(m => m.operation === operation);
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics = [];
    this.queryTracker = { count: 0, queries: [] };
    this.cacheStats = { hits: 0, misses: 0 };
  }

  /**
   * Log performance summary to console
   */
  logSummary(): void {
    const summary = this.getSummary();
    console.log('🚀 Performance Summary:');
    console.log(`   Operations: ${summary.totalOperations}`);
    console.log(`   Avg Duration: ${summary.averageDuration.toFixed(2)}ms`);
    console.log(`   Total Queries: ${summary.totalQueries}`);
    console.log(`   Cache Hit Rate: ${summary.cacheHitRate.toFixed(1)}%`);
    
    if (summary.recentMetrics.length > 0) {
      console.log('\n📊 Recent Operations:');
      summary.recentMetrics.forEach(metric => {
        console.log(`   ${metric.operation}: ${metric.duration.toFixed(2)}ms (${metric.queryCount} queries)`);
      });
    }
  }

  /**
   * Compare performance before and after optimization
   */
  comparePerformance(
    beforeMetrics: PerformanceMetric[],
    afterMetrics: PerformanceMetric[]
  ): {
    durationImprovement: number;
    queryReduction: number;
    summary: string;
  } {
    const beforeAvg = beforeMetrics.reduce((sum, m) => sum + m.duration, 0) / beforeMetrics.length;
    const afterAvg = afterMetrics.reduce((sum, m) => sum + m.duration, 0) / afterMetrics.length;
    
    const beforeQueries = beforeMetrics.reduce((sum, m) => sum + (m.queryCount || 0), 0);
    const afterQueries = afterMetrics.reduce((sum, m) => sum + (m.queryCount || 0), 0);
    
    const durationImprovement = ((beforeAvg - afterAvg) / beforeAvg) * 100;
    const queryReduction = beforeQueries > 0 ? ((beforeQueries - afterQueries) / beforeQueries) * 100 : 0;
    
    const summary = `
Performance Comparison:
• Duration: ${durationImprovement > 0 ? 'improved' : 'degraded'} by ${Math.abs(durationImprovement).toFixed(1)}%
• Queries: ${queryReduction > 0 ? 'reduced' : 'increased'} by ${Math.abs(queryReduction).toFixed(1)}%
• Before: ${beforeAvg.toFixed(2)}ms avg, ${beforeQueries} queries
• After: ${afterAvg.toFixed(2)}ms avg, ${afterQueries} queries
    `.trim();

    return {
      durationImprovement,
      queryReduction,
      summary,
    };
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * Decorator for monitoring function performance
 */
export function monitorPerformance(operationName: string) {
  return function <T extends (...args: any[]) => any>(
    target: any,
    propertyKey: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const originalMethod = descriptor.value!;

    descriptor.value = (async function (this: any, ...args: any[]) {
      const endTimer = performanceMonitor.startTimer(`${operationName}.${propertyKey}`);
      
      try {
        const result = await originalMethod.apply(this, args);
        const metric = endTimer();
        
        if (process.env.NODE_ENV === 'development') {
          console.log(`⚡ ${operationName}.${propertyKey}: ${metric.duration.toFixed(2)}ms (${metric.queryCount} queries)`);
        }
        
        return result;
      } catch (error) {
        endTimer();
        throw error;
      }
    }) as any;

    return descriptor;
  };
}

/**
 * Simple performance timing utility
 */
export async function measurePerformance<T>(
  operation: string,
  fn: () => Promise<T>
): Promise<{ result: T; metric: PerformanceMetric }> {
  const endTimer = performanceMonitor.startTimer(operation);
  
  try {
    const result = await fn();
    const metric = endTimer();
    return { result, metric };
  } catch (error) {
    endTimer();
    throw error;
  }
}
